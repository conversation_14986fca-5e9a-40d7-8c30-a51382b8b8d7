# AmoraOS Documentation

This directory contains documentation for the AmoraOS project, including the edge device implementation and the AmoraSDK.

## Viewing Documentation

To view the documentation locally, run:

```bash
./serve_docs.sh
```

Then open http://127.0.0.1:8000/ in your browser.

## Documentation Structure

- **User Guide**: Getting started and configuration
- **Developer Guide**: Architecture and development information
- **IoT Integration**: Azure IoT Hub integration details
- **Real-time Communication**: MQTT-based real-time communication
- **SDK**: Client and device integration guides

## Contributing to Documentation

1. Add or edit Markdown files in the `docs/site_docs` directory
2. Update the navigation in `docs/mkdocs.yml` if needed
3. Run `./serve_docs.sh` to preview changes
4. Commit your changes
