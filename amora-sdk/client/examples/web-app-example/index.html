<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Amora Music Player</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    .player-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .album-art {
      width: 300px;
      height: 300px;
      object-fit: cover;
      margin-bottom: 20px;
    }
    .player-controls {
      margin: 20px 0;
    }
    .playlist-container {
      max-height: 400px;
      overflow-y: auto;
    }
    .playlist-item {
      cursor: pointer;
    }
    .playlist-item.active {
      background-color: rgba(13, 110, 253, 0.1);
    }
    .connection-status {
      position: fixed;
      top: 10px;
      right: 10px;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 12px;
    }
    .connection-status.connected {
      background-color: #d4edda;
      color: #155724;
    }
    .connection-status.disconnected {
      background-color: #f8d7da;
      color: #721c24;
    }
    .connection-status.connecting {
      background-color: #fff3cd;
      color: #856404;
    }
    .progress {
      height: 10px;
      margin-bottom: 10px;
    }
    .song-info {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container player-container">
    <div class="connection-status disconnected" id="connectionStatus">Disconnected</div>
    
    <h1 class="text-center mb-4">Amora Music Player</h1>
    
    <div class="row">
      <div class="col-md-6">
        <div class="text-center">
          <img src="placeholder-album.jpg" alt="Album Art" class="album-art" id="albumArt">
          
          <div class="song-info">
            <h3 id="songTitle">No song playing</h3>
            <p id="songArtist">-</p>
            <p id="songAlbum">-</p>
          </div>
          
          <div class="progress">
            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
          </div>
          
          <div class="d-flex justify-content-between mb-3">
            <span id="currentTime">0:00</span>
            <span id="duration">0:00</span>
          </div>
          
          <div class="player-controls">
            <button class="btn btn-outline-secondary" id="prevButton">
              <i class="bi bi-skip-backward-fill"></i> Previous
            </button>
            <button class="btn btn-primary" id="playPauseButton">
              <i class="bi bi-play-fill" id="playPauseIcon"></i> Play
            </button>
            <button class="btn btn-outline-secondary" id="stopButton">
              <i class="bi bi-stop-fill"></i> Stop
            </button>
            <button class="btn btn-outline-secondary" id="nextButton">
              <i class="bi bi-skip-forward-fill"></i> Next
            </button>
          </div>
          
          <div class="mt-3">
            <label for="volumeSlider" class="form-label">Volume: <span id="volumeValue">0</span>%</label>
            <input type="range" class="form-range" id="volumeSlider" min="0" max="100" value="0">
          </div>
          
          <div class="mt-3">
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" id="repeatCheck">
              <label class="form-check-label" for="repeatCheck">Repeat</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="checkbox" id="randomCheck">
              <label class="form-check-label" for="randomCheck">Random</label>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <h4>Playlists</h4>
        <div class="mb-3">
          <select class="form-select" id="playlistSelect">
            <option value="">Select a playlist</option>
          </select>
          <button class="btn btn-outline-primary mt-2" id="loadPlaylistButton">Load Playlist</button>
        </div>
        
        <h4>Current Playlist</h4>
        <div class="playlist-container">
          <ul class="list-group" id="playlistItems">
            <li class="list-group-item">No items in playlist</li>
          </ul>
        </div>
      </div>
    </div>
    
    <div class="mt-4">
      <h4>Connection</h4>
      <div class="row g-3">
        <div class="col-md-6">
          <label for="brokerUrl" class="form-label">MQTT Broker URL</label>
          <input type="text" class="form-control" id="brokerUrl" value="localhost">
        </div>
        <div class="col-md-6">
          <label for="brokerPort" class="form-label">MQTT Broker Port</label>
          <input type="number" class="form-control" id="brokerPort" value="1883">
        </div>
        <div class="col-md-6">
          <label for="deviceId" class="form-label">Device ID</label>
          <input type="text" class="form-control" id="deviceId" value="amora-player-001">
        </div>
        <div class="col-12">
          <button class="btn btn-success" id="connectButton">Connect</button>
          <button class="btn btn-danger" id="disconnectButton" disabled>Disconnect</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css"></script>
  <script src="https://cdn.jsdelivr.net/npm/amora-client-sdk/dist/bundle.js"></script>
  <script src="app.js"></script>
</body>
</html>
