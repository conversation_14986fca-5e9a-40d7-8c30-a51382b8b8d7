/* Amora Music Player Styles */

:root {
  --primary-color: #6200ea;
  --secondary-color: #3700b3;
  --background-color: #f5f5f5;
  --sidebar-bg: #ffffff;
  --main-content-bg: #ffffff;
  --text-color: #333333;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow-x: hidden;
}

.container-fluid {
  padding: 0;
  height: 100vh;
}

.row {
  margin: 0;
  height: 100%;
}

/* Sidebar Styles */
.sidebar {
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--border-color);
  padding: 20px;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.connection-status {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}

.connection-status.connected {
  background-color: var(--success-color);
  color: white;
}

.connection-status.disconnected {
  background-color: var(--error-color);
  color: white;
}

.connection-status.connecting {
  background-color: var(--warning-color);
  color: white;
}

.playlist-container {
  max-height: calc(100vh - 350px);
  overflow-y: auto;
  margin-top: 10px;
}

.playlist-item {
  cursor: pointer;
  transition: background-color 0.2s;
}

.playlist-item:hover {
  background-color: rgba(98, 0, 234, 0.05);
}

.playlist-item.active {
  background-color: rgba(98, 0, 234, 0.1);
  border-left: 3px solid var(--primary-color);
}

.playlist-item-empty {
  text-align: center;
  color: #888;
  font-style: italic;
}

/* Main Content Styles */
.main-content {
  background-color: var(--main-content-bg);
  padding: 20px;
  height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.player-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.now-playing-container {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.album-art-container {
  width: 300px;
  height: 300px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.album-art {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.track-info {
  width: 100%;
  text-align: center;
}

.track-info h2 {
  margin-bottom: 5px;
  font-weight: bold;
}

.track-info p {
  margin-bottom: 5px;
  color: #666;
}

.progress-container {
  display: flex;
  align-items: center;
  margin: 20px 0;
  width: 100%;
}

.progress {
  flex: 1;
  height: 6px;
  margin: 0 10px;
  background-color: #e0e0e0;
}

.progress-bar {
  background-color: var(--primary-color);
}

.time-display {
  font-size: 14px;
  color: #666;
  min-width: 40px;
}

.controls {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.control-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin: 0 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  border: 1px solid var(--border-color);
  color: var(--primary-color);
  font-size: 20px;
  transition: all 0.2s;
}

.control-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.play-btn {
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  color: white;
}

.play-btn:hover {
  background-color: var(--secondary-color);
}

.volume-container {
  display: flex;
  align-items: center;
  margin: 20px 0;
  width: 100%;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.volume-container i {
  margin: 0 10px;
  color: #666;
}

.volume-container span {
  margin-left: 10px;
  color: #666;
  min-width: 30px;
}

.form-range {
  flex: 1;
}

.playback-options {
  margin: 20px 0;
}

.connection-panel {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .sidebar {
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .main-content {
    height: auto;
  }
  
  .now-playing-container {
    flex-direction: column;
  }
  
  .album-art-container {
    width: 200px;
    height: 200px;
  }
  
  .controls {
    flex-wrap: wrap;
  }
  
  .control-btn {
    margin: 5px;
  }
}
