[tool.poetry]
name = "amora-sdk"
version = "0.1.0"
description = "SDK for controlling AmoraOS player device"
authors = ["AmoraOS Team"]
readme = "README.md"
packages = [{include = "amora_sdk"}]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.110.0"
uvicorn = "^0.27.0"
websockets = "^12.0"
python-mpd2 = "^3.0.5"
pydantic = "^2.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.3.1"
pytest-cov = "^4.1.0"
black = "^23.3.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.3.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
amora-server = "amora_sdk.server:main"

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88
