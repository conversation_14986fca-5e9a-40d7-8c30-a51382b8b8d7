[tool.poetry]
name = "mqtt-test-app"
version = "0.1.0"
description = "MQTT Test Application for AmoraSDK"
authors = ["AmoraOS Team"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.9"
paho-mqtt = "^2.0.0"
python-mpd2 = "^3.0.5"
asyncio = "^3.4.3"
pydantic = "^2.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.3.1"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
coverage = "^7.2.7"
black = "^23.3.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.3.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
asyncio_mode = "auto"
