# Test Notes

## Testing Commands Found

1. Basic test command:
```bash
npm test
```
Location: README.md

2. MQTT Communication Testing Commands:
```bash
# Subscribe to all topics for a device
mosquitto_sub -h localhost -p 1883 -t "amora/devices/amora-player-001/#" -v

# Publish a test command
mosquitto_pub -h localhost -p 1883 -t "amora/devices/amora-player-001/commands" -m '{"command":"play","commandId":"test-123","timestamp":1616161616161}'

# Monitor state updates
mosquitto_sub -h localhost -p 1883 -t "amora/devices/amora-player-001/state" -v
```
Location: docs/MQTTTopics.md

## Test Tools Mentioned
- MQTT Explorer (http://mqtt-explorer.com/)
- Mosquitto CLI (https://mosquitto.org/man/mosquitto_pub-1.html)

## Notes
- No explicit CI configuration found
- Project appears to use Je<PERSON> for testing (found reference to jest.config.js)

## Installed Tool Versions

The following tools were installed and verified:

- rustc: 1.88.0 (6b00bc388 2025-06-23)
- cargo: 1.88.0 (873a06493 2025-05-10)
- qemu-system-x86_64: 8.2.2 (Debian 1:8.2.2+ds-0ubuntu1.7)
- cargo-binutils: 0.3.6

## Environment Variables

The following environment variables were set:

- RUST_TARGET_PATH=.cargo
