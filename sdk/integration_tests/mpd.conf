music_directory     "/var/lib/mpd/music"
playlist_directory  "/var/lib/mpd/playlists"
db_file             "/var/lib/mpd/tag_cache"
state_file          "/var/lib/mpd/state"
sticker_file        "/var/lib/mpd/sticker.sql"
log_file            "syslog"

user                "joao"
bind_to_address     "localhost"
port                "6600"

input {
    plugin "curl"
}

audio_output {
    type            "alsa"
    name            "ALSA Sound Card"
    mixer_type      "software"
    enabled         "yes"
}

audio_output {
    type            "pulse"
    name            "Pulseaudio Output"
    enabled         "yes"
}

audio_output {
    type            "null"
    name            "Null Output"
    enabled         "yes"
}

filesystem_charset  "UTF-8"
