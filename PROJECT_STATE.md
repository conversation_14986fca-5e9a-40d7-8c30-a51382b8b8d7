# AmoraOS Project State Documentation

## Overview
AmoraOS is an IoT edge audio platform currently in early development (MVP stage) focused on Raspberry Pi music boxes with cloud connectivity. The project migrated from Arch Linux to Debian Bookworm and includes a comprehensive SDK for seamless integration.

## Current Development Branch
- **Active Branch**: `feature/web-player-ui`
- **Base Branch**: `main`

## Recent Development Activity
Latest commits show active development on web components and client SDK:
- `8d7ac03` - Add Amora Player web application frontend
- `bab6f0a` - Add Amora Client SDK for web applications using MQTT
- `49dec51` - Add Azure MQTT broker integration documentation
- `454298f` - feat: test app mqtt broker
- `43eced4` - Add tests and local development configuration for MQTT test application

## Project Architecture

### Core Components

#### 1. Edge Device (`edge/`)
- **Platform**: Debian Bookworm on Raspberry Pi
- **Audio**: MPD (Music Player Daemon) with Pipewire backend
- **Hardware**: IQUADIO PI DA audio HAT support
- **Container**: Docker-based deployment
- **Language**: Python 3.9+

#### 2. SDK (`sdk/`)
- **Package**: `amora-sdk` v0.1.0
- **Language**: Python 3.9+
- **Dependencies**: FastAPI, uvicorn, websockets, python-mpd2, pydantic
- **Modules**:
  - Device module (broker, IoT, player)
  - MQTT broker integration
  - Azure IoT Hub integration
  - Music player implementation

#### 3. Client SDK (`amora-sdk/client/`)
- **Package**: `amora-client-sdk` v0.1.0
- **Language**: TypeScript/JavaScript
- **Dependencies**: MQTT.js
- **Purpose**: Web applications controlling music player devices

#### 4. Web Player (`web-player/`)
- **Type**: Frontend web application
- **Purpose**: User interface for music control
- **Files**: HTML, CSS, JavaScript with Amora SDK integration

## Key Features

### Implemented
- ✅ Python-based edge audio player with MPD/MPC
- ✅ Pipewire audio backend
- ✅ MQTT broker integration for real-time communication
- ✅ Azure IoT Hub integration for device management
- ✅ Device twin synchronization
- ✅ Direct method invocation for remote control
- ✅ Comprehensive test suite (unit + integration)
- ✅ Docker containerization
- ✅ TypeScript/JavaScript client SDK
- ✅ Web player frontend

### In Development
- 🔄 Web player UI enhancements
- 🔄 Client SDK web integration
- 🔄 MQTT test applications

### Planned
- ⏳ Content synchronization from Azure Blob Storage
- ⏳ Over-the-air updates
- ⏳ Fleet management capabilities
- ⏳ React component library
- ⏳ Enhanced security with certificate-based auth

## Technology Stack

### Backend
- **Python 3.9+** with Poetry dependency management
- **FastAPI** for REST API
- **WebSockets** for real-time communication
- **MQTT** for device communication
- **Azure IoT Hub** for cloud integration
- **MPD (Music Player Daemon)** for audio control
- **Pipewire** for audio routing

### Frontend
- **TypeScript/JavaScript** client SDK
- **MQTT.js** for browser-based MQTT communication
- **HTML/CSS/JS** web player interface

### Infrastructure
- **Docker** containers (Debian Bookworm)
- **Azure IoT Hub** for device management
- **Azure Event Hub** for telemetry
- **MQTT Broker** for real-time messaging

## Testing & Quality

### Test Coverage
- Unit tests for all core modules
- Integration tests for device functionality
- Mock frameworks for hardware-independent testing
- Comprehensive test configurations

### Code Quality Tools
- **Python**: pytest, black, isort, flake8, mypy
- **TypeScript**: jest, eslint, prettier, typedoc

## Configuration

### Device Configuration (`edge/config/`)
- `config.json` - Production configuration
- `config.dev.json` - Development configuration
- `mpd.conf` - MPD daemon configuration
- Audio and device settings

### Development Environment
- Poetry for Python dependency management
- npm for JavaScript/TypeScript dependencies
- Docker for containerized development
- Local documentation server with MkDocs

## Current Status

### Active Development Areas
1. **Web Player UI** - Frontend interface improvements
2. **Client SDK Integration** - Web application SDK enhancements
3. **MQTT Testing** - Real-time communication validation

### Untracked Files (Pending)
- `amora-sdk/client/TEST_NOTES.md`
- `amora-sdk/client/package-lock.json`
- `edge/poetry.lock`
- `sdk/poetry.lock`

### Next Steps
1. Complete web player UI implementation
2. Finalize client SDK web integration
3. Comprehensive MQTT broker testing
4. Prepare for MVP release

## Documentation

### Available Documentation
- Comprehensive docs in `/docs/` with MkDocs
- API documentation for client SDK
- Azure integration guides
- Developer guides and getting started

### Documentation Server
```bash
./serve_docs.sh  # Serves docs at http://127.0.0.1:8000/
```

## Development Setup

### Prerequisites
- Linux development environment (Windows not supported)
- Python 3.9+ with Poetry
- Node.js for client SDK development
- Docker for containerized deployment

### Quick Start
```bash
# Python SDK
cd sdk/
poetry install
poetry run pytest

# Client SDK
cd amora-sdk/client/
npm install
npm test

# Documentation
./serve_docs.sh
```

---

**Last Updated**: 2025-07-19  
**Project Version**: v0.1.0 (MVP)  
**Status**: Active Development